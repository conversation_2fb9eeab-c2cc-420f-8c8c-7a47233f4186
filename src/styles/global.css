@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
@layer base {
  body {
    @apply bg-gradient-to-br from-indigo-50 via-white to-cyan-100 min-h-screen;
    font-family: 'Inter', system-ui, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Liquid Glass 组件样式 */
@layer components {
  /* 玻璃态容器 */
  .glass-container {
    @apply bg-white/20 backdrop-blur-lg border border-white/30 rounded-3xl shadow-xl;
    transition: all 0.3s ease;
  }

  .glass-card {
    @apply bg-white/15 backdrop-blur-md border border-white/20 rounded-2xl shadow-lg;
    transition: all 0.3s ease;
  }

  .glass-card:hover {
    @apply bg-white/25 shadow-xl;
    transform: translateY(-2px);
  }

  /* 液体按钮 */
  .liquid-btn {
    @apply px-6 py-3 rounded-full font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .liquid-btn:hover {
    transform: translateY(-1px);
  }

  /* 筛选按钮 */
  .filter-btn {
    @apply px-4 py-2 rounded-full bg-white/30 backdrop-blur-sm border border-white/40 text-gray-700 font-medium transition-all duration-300 hover:bg-white/40;
  }

  .filter-btn.active {
    @apply bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg;
  }

  /* 订阅卡片 */
  .subscription-card {
    @apply glass-card p-6 transition-all duration-300 hover:scale-105;
  }

  /* 状态指示器 - 液体风格 */
  .status-expired {
    @apply border-l-4 border-red-500;
  }

  .status-today {
    @apply border-l-4 border-red-500;
  }

  .status-warning {
    @apply border-l-4 border-yellow-500;
  }

  .status-caution {
    @apply border-l-4 border-orange-500;
  }

  .status-normal {
    @apply border-l-4 border-green-500;
  }

  /* 状态标签 - 现代化样式 */
  .status-badge-expired {
    @apply bg-gradient-to-r from-red-500 to-red-600 text-white border-0 shadow-red-500/25;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }

  .status-badge-today {
    @apply bg-gradient-to-r from-red-500 to-red-600 text-white border-0 shadow-red-500/25;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }

  .status-badge-warning {
    @apply bg-gradient-to-r from-amber-400 to-orange-500 text-white border-0 shadow-amber-500/25;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  }

  .status-badge-caution {
    @apply bg-gradient-to-r from-orange-400 to-orange-600 text-white border-0 shadow-orange-500/25;
    background: linear-gradient(135deg, #fb923c 0%, #ea580c 100%);
  }

  .status-badge-normal {
    @apply bg-gradient-to-r from-emerald-400 to-green-600 text-white border-0 shadow-emerald-500/25;
    background: linear-gradient(135deg, #34d399 0%, #059669 100%);
  }

  /* 状态指示点颜色 */
  .status-dot-expired {
    @apply bg-red-500;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
  }

  .status-dot-today {
    @apply bg-red-500;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
  }

  .status-dot-warning {
    @apply bg-yellow-400;
    box-shadow: 0 0 8px rgba(251, 191, 36, 0.6);
  }

  .status-dot-caution {
    @apply bg-orange-400;
    box-shadow: 0 0 8px rgba(251, 146, 60, 0.6);
  }

  .status-dot-normal {
    @apply bg-green-400;
    box-shadow: 0 0 8px rgba(52, 211, 153, 0.6);
  }

  /* 统计卡片 */
  .stats-card {
    @apply glass-card p-6 text-center;
  }

  .stats-number {
    @apply text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* 导航栏玻璃态 */
  .nav-glass {
    @apply bg-white/80 backdrop-blur-lg border-b border-white/20;
  }

  /* 浮动元素 */
  .floating-element {
    @apply animate-pulse;
  }

  .floating-element-delayed {
    @apply animate-pulse;
  }

  /* 渐变文字 */
  .gradient-text {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* 液体背景装饰 */
  .liquid-bg {
    position: relative;
  }
}

/* 工具类 */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 波纹效果 */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
