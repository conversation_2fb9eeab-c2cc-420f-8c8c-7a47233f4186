---
import Layout from '../layouts/Layout.astro';
import { getCollection } from 'astro:content';

const subscriptions = await getCollection('subscriptions');

// 计算到期状态的函数
function getExpiryStatus(nextBillingDate: Date) {
  const now = new Date();
  const diffTime = nextBillingDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return { status: 'expired', days: Math.abs(diffDays), text: `已过期 ${Math.abs(diffDays)} 天`, icon: '⚠️' };
  } else if (diffDays === 0) {
    return { status: 'today', days: 0, text: '今天到期', icon: '🔥' };
  } else if (diffDays <= 3) {
    return { status: 'warning', days: diffDays, text: `${diffDays} 天后到期`, icon: '⏰' };
  } else if (diffDays <= 7) {
    return { status: 'caution', days: diffDays, text: `${diffDays} 天后到期`, icon: '📅' };
  } else {
    return { status: 'normal', days: diffDays, text: `${diffDays} 天后到期`, icon: '✅' };
  }
}

// 格式化价格
function formatPrice(price: number, currency: string) {
  const symbols = {
    'CNY': '¥',
    'USD': '$',
    'EUR': '€'
  };
  return `${symbols[currency] || currency} ${price}`;
}

// 格式化计费周期
function formatBillingCycle(cycle: string) {
  const cycles = {
    'monthly': '月付',
    'yearly': '年付',
    'quarterly': '季付',
    'weekly': '周付'
  };
  return cycles[cycle] || cycle;
}

// 按到期时间排序
const sortedSubscriptions = subscriptions
  .map(sub => ({
    ...sub,
    expiryStatus: getExpiryStatus(sub.data.nextBillingDate)
  }))
  .sort((a, b) => a.data.nextBillingDate.getTime() - b.data.nextBillingDate.getTime());

// 计算统计数据
const stats = {
  total: subscriptions.length,
  expired: sortedSubscriptions.filter(s => s.expiryStatus.status === 'expired').length,
  warning: sortedSubscriptions.filter(s => ['warning', 'today'].includes(s.expiryStatus.status)).length,
  normal: sortedSubscriptions.filter(s => s.expiryStatus.status === 'normal').length,
  monthlyTotal: subscriptions.reduce((sum, sub) => {
    const monthlyPrice = sub.data.billingCycle === 'yearly' ? sub.data.price / 12 :
                       sub.data.billingCycle === 'quarterly' ? sub.data.price / 3 :
                       sub.data.billingCycle === 'weekly' ? sub.data.price * 4 :
                       sub.data.price;
    return sum + (sub.data.currency === 'CNY' ? monthlyPrice : monthlyPrice * 7.2);
  }, 0)
};
---

<Layout title="Moatkon's Sub">
  <!-- 如果没有订阅数据，显示欢迎信息 -->
  {subscriptions.length === 0 ? (
    <div class="text-center py-16">
      <div class="glass-container max-w-2xl mx-auto p-12">
        <div class="w-24 h-24 bg-gradient-to-br from-liquid-primary to-liquid-secondary rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-liquid">
          <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold gradient-text mb-4">开始管理您的订阅</h3>
        <p class="text-gray-600 mb-8">添加您的第一个订阅服务，开始优雅地管理您的数字生活</p>
        <button class="liquid-btn">
          <span>添加订阅</span>
        </button>
      </div>
    </div>
  ) : (
    <div class="space-y-12">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- 总订阅数 -->
      <div class="stats-card group">
        <div class="relative z-10">
          <div class="flex items-center justify-center mb-4">
            <div class="w-16 h-16 bg-gradient-to-br from-liquid-primary to-liquid-secondary rounded-2xl flex items-center justify-center shadow-liquid">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
          </div>
          <div class="text-center">
            <p class="text-sm font-medium text-gray-600 mb-2">总订阅数</p>
            <p class="stats-number">{stats.total}</p>
            <p class="text-xs text-gray-500 mt-1">活跃服务</p>
          </div>
        </div>
      </div>

      <!-- 已过期 -->
      <div class="stats-card group">
        <div class="relative z-10">
          <div class="flex items-center justify-center mb-4">
            <div class="w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center shadow-liquid">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
          </div>
          <div class="text-center">
            <p class="text-sm font-medium text-gray-600 mb-2">已过期</p>
            <p class="text-3xl font-bold bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">{stats.expired}</p>
            <p class="text-xs text-gray-500 mt-1">需要处理</p>
          </div>
        </div>
      </div>

      <!-- 即将到期 -->
      <div class="stats-card group">
        <div class="relative z-10">
          <div class="flex items-center justify-center mb-4">
            <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-liquid">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="text-center">
            <p class="text-sm font-medium text-gray-600 mb-2">即将到期</p>
            <p class="text-3xl font-bold bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">{stats.warning}</p>
            <p class="text-xs text-gray-500 mt-1">需要关注</p>
          </div>
        </div>
      </div>

      <!-- 月度支出 -->
      <div class="stats-card group">
        <div class="relative z-10">
          <div class="flex items-center justify-center mb-4">
            <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl flex items-center justify-center shadow-liquid">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
          <div class="text-center">
            <p class="text-sm font-medium text-gray-600 mb-2">月度支出</p>
            <p class="text-3xl font-bold bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent">¥{stats.monthlyTotal.toFixed(0)}</p>
            <p class="text-xs text-gray-500 mt-1">预估费用</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    {/* <div class="glass-container p-8">
      <div class="flex flex-col sm:flex-row items-center justify-between mb-6">
        <h3 class="text-xl font-semibold gradient-text mb-4 sm:mb-0">订阅服务</h3>
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
          </svg>
          <span class="text-sm text-gray-600">筛选条件</span>
        </div>
      </div>

      <div class="flex flex-wrap gap-3">
        <button class="filter-btn active" data-filter="all">
          <span class="flex items-center space-x-2">
            <span>📋</span>
            <span>全部 ({stats.total})</span>
          </span>
        </button>
        <button class="filter-btn" data-filter="expired">
          <span class="flex items-center space-x-2">
            <span>⚠️</span>
            <span>已过期 ({stats.expired})</span>
          </span>
        </button>
        <button class="filter-btn" data-filter="warning">
          <span class="flex items-center space-x-2">
            <span>⏰</span>
            <span>即将到期 ({stats.warning})</span>
          </span>
        </button>
        <button class="filter-btn" data-filter="normal">
          <span class="flex items-center space-x-2">
            <span>✅</span>
            <span>正常 ({stats.normal})</span>
          </span>
        </button>
      </div>
    </div> */}

    <!-- 订阅列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="subscriptions-grid">
      {sortedSubscriptions.map((subscription) => {
        const { data } = subscription;
        const status = subscription.expiryStatus;

        return (
          <div class={`subscription-card status-${status.status} group`} data-status={status.status}>
            <div class="relative">
              

              <!-- 卡片头部 -->
              <div class="mb-6">
                <div class="flex items-start space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-liquid-primary to-liquid-secondary rounded-xl flex items-center justify-center flex-shrink-0 shadow-liquid">
                    <span class="text-white font-bold text-lg">
                      {data.title.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-bold text-gray-900 mb-1 truncate">
                      {data.title}
                    </h3>
                    <p class="text-sm text-gray-600 font-medium">{data.service}</p>
                  </div>
                </div>
              </div>

              <!-- 状态指示器 -->
              <div class="mb-6">
                <div class="flex items-start space-x-4">
                  <div class={`status-badge-${status.status} flex items-center space-x-2 px-3 py-2 rounded-xl shadow-lg backdrop-blur-sm`}>
                      <div class={`w-2 h-2 rounded-full animate-pulse ${
                        status.status === 'expired' ? 'bg-red-500' :
                        status.status === 'today' ? 'bg-red-500' :
                        status.status === 'warning' ? 'bg-yellow-400' :
                        status.status === 'caution' ? 'bg-orange-400' :
                        'bg-green-400'
                      }`} style={`box-shadow: 0 0 8px ${
                        status.status === 'expired' ? 'rgba(239, 68, 68, 0.6)' :
                        status.status === 'today' ? 'rgba(239, 68, 68, 0.6)' :
                        status.status === 'warning' ? 'rgba(251, 191, 36, 0.6)' :
                        status.status === 'caution' ? 'rgba(251, 146, 60, 0.6)' :
                        'rgba(52, 211, 153, 0.6)'
                      }`}></div>
                      <span class="text-xs font-semibold tracking-wide">{status.text}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 价格信息 -->
              <div class="mb-6">
                <div class="glass-card p-4">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-600">订阅费用</span>
                    <span class="text-lg font-bold gradient-text">
                      {formatPrice(data.price, data.currency)}
                    </span>
                  </div>
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">计费周期</span>
                    <span class="text-gray-700 font-medium">{formatBillingCycle(data.billingCycle)}</span>
                  </div>
                </div>
              </div>

              <!-- 到期信息 -->
              <div class="mb-6">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm text-gray-600">下次扣费</p>
                    <p class="text-sm font-semibold text-gray-900">
                      {data.nextBillingDate.toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                </div>
              </div>

              <!-- 分类和标签 -->
              {(data.category || (data.tags && data.tags.length > 0)) && (
                <div class="mb-6 space-y-3">
                  {data.category && (
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-500">分类:</span>
                      <span class="px-2 py-1 bg-gradient-to-r from-liquid-primary/10 to-liquid-secondary/10 text-liquid-primary text-xs rounded-full font-medium">
                        {data.category}
                      </span>
                    </div>
                  )}

                  {/* {data.tags && data.tags.length > 0 && (
                    <div class="flex flex-wrap gap-1">
                      {data.tags.map((tag: string) => (
                        <span class="px-2 py-1 bg-glass-400 text-gray-700 text-xs rounded-full backdrop-blur-sm">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )} */}
                </div>
              )}

              <!-- 操作按钮 -->
              <div class="flex space-x-3">
                {data.website && (
                  <a
                    href={data.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="flex-1 liquid-btn text-center text-sm py-2"
                  >
                    <span>续费地址</span>
                  </a>
                )}
              
              </div>
            </div>
          </div>
        );
      })}
    </div>
    </div>
  )}

  <script>
    // 筛选功能和动画效果
    document.addEventListener('DOMContentLoaded', function() {
      const filterButtons = document.querySelectorAll('.filter-btn');
      const subscriptionCards = document.querySelectorAll('.subscription-card');

      // 筛选功能
      filterButtons.forEach(button => {
        button.addEventListener('click', function(this: HTMLElement) {
          // 更新按钮状态
          filterButtons.forEach(btn => btn.classList.remove('active'));
          this.classList.add('active');

          const filter = this.dataset.filter;

          // 筛选卡片 - 添加淡入淡出动画
          subscriptionCards.forEach((card, index) => {
            const htmlCard = card as HTMLElement;
            const status = htmlCard.dataset.status;
            let shouldShow = false;

            if (filter === 'all') {
              shouldShow = true;
            } else if (filter === 'expired' && status === 'expired') {
              shouldShow = true;
            } else if (filter === 'warning' && (status === 'warning' || status === 'today')) {
              shouldShow = true;
            } else if (filter === 'normal' && (status === 'normal' || status === 'caution')) {
              shouldShow = true;
            }

            if (shouldShow) {
              htmlCard.style.display = 'block';
              // 添加延迟动画效果
              setTimeout(() => {
                htmlCard.style.opacity = '1';
                htmlCard.style.transform = 'translateY(0)';
              }, index * 50);
            } else {
              htmlCard.style.opacity = '0';
              htmlCard.style.transform = 'translateY(20px)';
              setTimeout(() => {
                htmlCard.style.display = 'none';
              }, 300);
            }
          });
        });
      });

      // 初始化卡片动画
      subscriptionCards.forEach((card, index) => {
        const htmlCard = card as HTMLElement;
        htmlCard.style.opacity = '0';
        htmlCard.style.transform = 'translateY(20px)';
        htmlCard.style.transition = 'all 0.3s ease';

        // 延迟显示每个卡片
        setTimeout(() => {
          htmlCard.style.opacity = '1';
          htmlCard.style.transform = 'translateY(0)';
        }, index * 100);
      });

      // 添加鼠标跟踪效果
      subscriptionCards.forEach(card => {
        const htmlCard = card as HTMLElement;

        htmlCard.addEventListener('mousemove', function(e: MouseEvent) {
          const rect = this.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;

          const centerX = rect.width / 2;
          const centerY = rect.height / 2;

          const rotateX = (y - centerY) / 20;
          const rotateY = (centerX - x) / 20;

          this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });

        htmlCard.addEventListener('mouseleave', function() {
          this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
        });
      });

      // 添加统计卡片的悬停效果
      const statsCards = document.querySelectorAll('.stats-card');
      statsCards.forEach((card, index) => {
        const htmlCard = card as HTMLElement;

        // 初始动画
        htmlCard.style.opacity = '0';
        htmlCard.style.transform = 'translateY(30px)';
        htmlCard.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';

        setTimeout(() => {
          htmlCard.style.opacity = '1';
          htmlCard.style.transform = 'translateY(0)';
        }, index * 150);

        // 悬停效果
        htmlCard.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        htmlCard.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });

      // 添加按钮点击波纹效果
      const buttons = document.querySelectorAll('.liquid-btn, .filter-btn');
      buttons.forEach(button => {
        const htmlButton = button as HTMLElement;
        htmlButton.style.position = 'relative';
        htmlButton.style.overflow = 'hidden';

        htmlButton.addEventListener('click', function(e) {
          const mouseEvent = e as MouseEvent;
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = mouseEvent.clientX - rect.left - size / 2;
          const y = mouseEvent.clientY - rect.top - size / 2;

          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('ripple');

          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });

      // 添加滚动视差效果
      window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-element, .floating-element-delayed');

        parallaxElements.forEach((element, index) => {
          const htmlElement = element as HTMLElement;
          const speed = 0.5 + (index * 0.1);
          const yPos = -(scrolled * speed);
          htmlElement.style.transform = `translateY(${yPos}px)`;
        });
      });
    });
  </script>
</Layout>
